#!/usr/bin/env python3
"""
验证改进算法的几何关系
"""

import sys
import os
import math

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from utils.geometry import Point3D, Vector3D


def verify_geometric_constraints():
    """验证几何约束"""
    
    print("🔍 几何约束验证")
    print("=" * 60)
    
    # 使用case02的实际数据
    top = Point3D(-68.3425, 191.373, 222.918)
    bottom = Point3D(-69.1684, 200.974, 208.913)
    center = Point3D(-62.2423, 198.69, 217.256)
    
    print(f"📍 输入坐标:")
    print(f"  top:    {top}")
    print(f"  bottom: {bottom}")
    print(f"  center: {center}")
    print()
    
    # 执行改进算法
    print("🔄 执行改进算法:")
    print("-" * 40)
    
    # 步骤1：三角平面法向量
    vector1 = top - center
    vector2 = bottom - center
    triangle_normal = vector1.cross(vector2).normalize()
    print(f"1. 三角平面法向量: {triangle_normal}")
    
    # 步骤2：上下螺钉向量
    screw_line_vector = top - bottom
    print(f"2. 上下螺钉向量:   {screw_line_vector}")
    
    # 步骤3：基座法向量
    base_normal = triangle_normal.cross(screw_line_vector).normalize()
    print(f"3. 基座法向量:     {base_normal}")
    
    # 步骤4：基座中心
    base_center = Point3D(
        (top.x + bottom.x) / 2,
        (top.y + bottom.y) / 2,
        (top.z + bottom.z) / 2
    )
    print(f"4. 基座中心:       {base_center}")
    print()
    
    # 验证几何约束
    print("✅ 几何约束验证:")
    print("-" * 40)
    
    # 约束1：基座平面通过top和bottom螺钉
    print("约束1: 基座平面通过top和bottom螺钉")
    
    top_to_center = top - base_center
    bottom_to_center = bottom - base_center
    
    dot1 = top_to_center.dot(base_normal)
    dot2 = bottom_to_center.dot(base_normal)
    
    print(f"  top到基座中心向量 · 基座法向量 = {dot1:.8f}")
    print(f"  bottom到基座中心向量 · 基座法向量 = {dot2:.8f}")
    print(f"  ✓ 都应该接近0 (实际误差: {abs(dot1):.2e}, {abs(dot2):.2e})")
    print()
    
    # 约束2：基座法向量垂直于上下螺钉连线
    print("约束2: 基座法向量垂直于上下螺钉连线")
    
    dot3 = screw_line_vector.normalize().dot(base_normal)
    print(f"  上下螺钉向量 · 基座法向量 = {dot3:.8f}")
    print(f"  ✓ 应该接近0 (实际误差: {abs(dot3):.2e})")
    print()
    
    # 约束3：基座法向量垂直于三角平面法向量
    print("约束3: 基座法向量垂直于三角平面法向量")
    
    dot4 = triangle_normal.dot(base_normal)
    print(f"  三角平面法向量 · 基座法向量 = {dot4:.8f}")
    print(f"  ✓ 应该接近0 (实际误差: {abs(dot4):.2e})")
    print()
    
    # 计算一些有用的几何量
    print("📊 几何分析:")
    print("-" * 40)
    
    # center点到基座平面的距离
    center_to_base = center - base_center
    distance_to_plane = abs(center_to_base.dot(base_normal))
    print(f"center点到基座平面距离: {distance_to_plane:.3f} mm")
    
    # 上下螺钉距离
    screw_distance = screw_line_vector.magnitude()
    print(f"上下螺钉间距离: {screw_distance:.3f} mm")
    
    # 三角形面积
    triangle_area = vector1.cross(vector2).magnitude() / 2
    print(f"三个螺钉形成的三角形面积: {triangle_area:.3f} mm²")
    
    print()
    print("🎯 结论:")
    print("-" * 40)
    print("✓ 所有几何约束都得到满足")
    print("✓ 基座平面确实通过top和bottom螺钉")
    print("✓ 基座法向量考虑了center螺钉的空间位置")
    print(f"✓ center螺钉比基座平面深入 {distance_to_plane:.1f} mm")


if __name__ == "__main__":
    verify_geometric_constraints()
